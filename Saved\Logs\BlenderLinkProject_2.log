﻿Log file open, 06/02/25 07:05:42
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=36100)
LogWindows: Warning: Failed to set completion port for job object "UE.ShaderCompileWorker.JobGroup": The parameter is incorrect.
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 36858
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 1.421107
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-583163C944AAA8EBAE6576A810121132
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading Unix ini files took 0.04 seconds
LogConfig: Display: Loading Windows ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.07 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.09 seconds
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogConfig: Display: Loading Mac ini files took 0.15 seconds
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogAssetRegistry: Display: Asset registry cache read as 44.1 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 1.43ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.28ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.30ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.02-01.35.46:092][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.02-01.35.46:092][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.02-01.35.46:092][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.02-01.35.46:092][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.02-01.35.46:092][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.02-01.35.46:093][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.02-01.35.46:093][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.02-01.35.46:093][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.02-01.35.46:093][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.02-01.35.46:097][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.02-01.35.46:097][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.02-01.35.46:098][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.02-01.35.46:098][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.02-01.35.46:098][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.02-01.35.46:098][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.02-01.35.46:098][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.02-01.35.46:101][  0]LogRHI: Using Default RHI: D3D12
[2025.06.02-01.35.46:101][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.02-01.35.46:101][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.02-01.35.46:131][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.02-01.35.46:131][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.02-01.35.46:273][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.02-01.35.46:273][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.02-01.35.46:273][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.02-01.35.46:274][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.02-01.35.46:274][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.02-01.35.46:452][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.02-01.35.46:452][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.02-01.35.46:452][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.02-01.35.46:452][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.02-01.35.46:452][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.02-01.35.46:462][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.02-01.35.46:462][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.02-01.35.46:462][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.02-01.35.46:462][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.02-01.35.46:462][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.02-01.35.46:463][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.02-01.35.46:463][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.02-01.35.46:463][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.02-01.35.46:463][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.02-01.35.46:463][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-01.35.46:463][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.02-01.35.46:463][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.02-01.35.46:463][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.02-01.35.46:472][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.02-01.35.46:472][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.02-01.35.46:472][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.02-01.35.46:472][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.02-01.35.46:472][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.02-01.35.46:472][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.02-01.35.46:472][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.02-01.35.46:472][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.02-01.35.46:472][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.02-01.35.46:472][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.02-01.35.46:472][  0]LogInit: User: Shashank
[2025.06.02-01.35.46:472][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.02-01.35.46:472][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.02-01.35.47:328][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=79.0GB
[2025.06.02-01.35.47:328][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.02-01.35.47:328][  0]LogMemory: Process Physical Memory: 629.72 MB used, 668.64 MB peak
[2025.06.02-01.35.47:328][  0]LogMemory: Process Virtual Memory: 761.50 MB used, 761.50 MB peak
[2025.06.02-01.35.47:328][  0]LogMemory: Physical Memory: 44221.73 MB used,  21229.07 MB free, 65450.80 MB total
[2025.06.02-01.35.47:328][  0]LogMemory: Virtual Memory: 71599.20 MB used,  9340.96 MB free, 80940.16 MB total
[2025.06.02-01.35.47:328][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.02-01.35.47:334][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.02-01.35.47:397][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.02-01.35.47:397][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.02-01.35.47:398][  0]LogInit: Using OS detected language (en-GB).
[2025.06.02-01.35.47:398][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.02-01.35.47:514][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.02-01.35.47:514][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.02-01.35.48:127][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.02-01.35.48:127][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.02-01.35.48:127][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.02-01.35.48:304][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.02-01.35.48:304][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.02-01.35.48:806][  0]LogRHI: Using Default RHI: D3D12
[2025.06.02-01.35.48:806][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.02-01.35.48:806][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.02-01.35.48:806][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.02-01.35.48:806][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.02-01.35.48:806][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.02-01.35.48:806][  0]LogWindows: Attached monitors:
[2025.06.02-01.35.48:806][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.02-01.35.48:806][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.02-01.35.48:806][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.02-01.35.48:806][  0]LogWindows: Found 3 attached monitors.
[2025.06.02-01.35.48:806][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.02-01.35.48:806][  0]LogRHI: RHI Adapter Info:
[2025.06.02-01.35.48:806][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.02-01.35.48:806][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.02-01.35.48:806][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.02-01.35.48:807][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.02-01.35.48:838][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.02-01.35.48:934][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.02-01.35.48:934][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.02-01.35.49:016][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: Raster order views are supported
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.02-01.35.49:016][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008654ECD5300)
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008654ECD5580)
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008654ECD5800)
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.02-01.35.49:039][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.02-01.35.49:039][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.02-01.35.49:071][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.02-01.35.49:075][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.02-01.35.49:132][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.06.02-01.35.49:132][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.06.02-01.35.49:502][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.02-01.35.49:502][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.02-01.35.49:502][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.02-01.35.49:502][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.02-01.35.49:502][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.02-01.35.49:502][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.02-01.35.49:502][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.02-01.35.49:523][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.02-01.35.49:537][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.02-01.35.49:626][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.02-01.35.49:731][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.02-01.35.49:731][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.02-01.35.49:863][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.02-01.35.49:863][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.02-01.35.49:863][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.02-01.35.49:863][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.02-01.35.50:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.02-01.35.50:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.02-01.35.50:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.02-01.35.50:282][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.02-01.35.50:282][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.02-01.35.50:282][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.02-01.35.50:282][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.02-01.35.50:377][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.02-01.35.50:377][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.02-01.35.50:547][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.02-01.35.50:547][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.02-01.35.50:547][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.02-01.35.50:547][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.02-01.35.50:547][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.02-01.35.52:126][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.02-01.35.52:170][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.02-01.35.52:171][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.02-01.35.52:171][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.02-01.35.52:171][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.02-01.35.52:173][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.02-01.35.52:173][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.02-01.35.52:173][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.02-01.35.52:173][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.02-01.35.52:173][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.02-01.35.52:455][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.02-01.35.52:455][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.02-01.35.52:456][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.02-01.35.52:457][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.02-01.35.52:473][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.02-01.35.52:473][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.02-01.35.52:506][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.06.02-01.35.52:508][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.02-01.35.52:508][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.051 seconds
[2025.06.02-01.35.52:509][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.02-01.35.52:516][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.02-01.35.52:516][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.08ms. RandomReadSpeed=337.85MBs, RandomWriteSpeed=312.03MBs. Assigned SpeedClass 'Local'
[2025.06.02-01.35.52:517][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.02-01.35.52:517][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.02-01.35.52:517][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.02-01.35.52:517][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.02-01.35.52:517][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.02-01.35.52:517][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.02-01.35.52:517][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.02-01.35.52:518][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/21456/).
[2025.06.02-01.35.52:518][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/A7941C4C4A060B052C632B9E2F2A37B9/'.
[2025.06.02-01.35.52:518][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.02-01.35.52:518][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.02-01.35.52:519][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.02-01.35.52:519][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.02-01.36.02:712][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.02-01.36.04:174][  0]LogSlate: Using FreeType 2.10.0
[2025.06.02-01.36.04:210][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.02-01.36.04:211][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.02-01.36.04:211][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.02-01.36.04:338][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.02-01.36.04:338][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.02-01.36.04:338][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.02-01.36.04:338][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.02-01.36.04:571][  0]LogAssetRegistry: FAssetRegistry took 0.0022 seconds to start up
[2025.06.02-01.36.04:573][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.02-01.36.04:582][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.02-01.36.04:582][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.06.02-01.36.04:757][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-01.36.04:796][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.02-01.36.04:796][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.02-01.36.04:796][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.02-01.36.04:807][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.02-01.36.04:807][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.02-01.36.04:831][  0]LogDeviceProfileManager: Active device profile: [000008656A66FA00][00000865687C0000 66] WindowsEditor
[2025.06.02-01.36.04:831][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.02-01.36.04:831][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.02-01.36.04:853][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.02-01.36.04:853][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.02-01.36.05:239][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:240][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.02-01.36.05:240][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-01.36.05:240][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:240][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.02-01.36.05:240][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-01.36.05:241][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:241][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.02-01.36.05:241][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-01.36.05:241][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:241][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.02-01.36.05:241][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-01.36.05:242][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.02-01.36.05:243][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.02-01.36.05:244][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.02-01.36.05:245][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-01.36.05:246][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-01.36.05:246][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.02-01.36.05:246][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-01.36.05:584][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.02-01.36.05:663][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.02-01.36.05:663][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.06.02-01.36.05:663][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.02-01.36.05:663][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.02-01.36.05:663][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.02-01.36.06:556][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.02-01.36.06:675][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.02-01.36.06:731][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.02-01.36.06:732][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.02-01.36.08:006][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.02-01.36.08:006][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.02-01.36.08:032][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.02-01.36.08:032][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.02-01.36.08:033][  0]LogLiveCoding: Display: Detected running instance in process group "UE_BlenderLinkProject_0xe99fe6f9", connecting to console process (PID: 42740)
[2025.06.02-01.36.08:033][  0]LogLiveCoding: Display: Waiting for server
[2025.06.02-01.36.08:034][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.02-01.36.08:139][  0]LogSlate: Border
[2025.06.02-01.36.08:139][  0]LogSlate: BreadcrumbButton
[2025.06.02-01.36.08:139][  0]LogSlate: Brushes.Title
[2025.06.02-01.36.08:139][  0]LogSlate: Default
[2025.06.02-01.36.08:139][  0]LogSlate: Icons.Save
[2025.06.02-01.36.08:139][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.02-01.36.08:139][  0]LogSlate: ListView
[2025.06.02-01.36.08:139][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.02-01.36.08:139][  0]LogSlate: SoftwareCursor_Grab
[2025.06.02-01.36.08:139][  0]LogSlate: TableView.DarkRow
[2025.06.02-01.36.08:139][  0]LogSlate: TableView.Row
[2025.06.02-01.36.08:139][  0]LogSlate: TreeView
[2025.06.02-01.36.11:897][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.02-01.36.11:898][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.707 ms
[2025.06.02-01.36.11:953][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.02-01.36.12:085][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.02-01.36.12:085][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.02-01.36.12:085][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.02-01.36.12:085][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.02-01.36.12:406][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 41E74E0878AC49178000000000008900 | Instance: 42EC07BE41485A0F5243AD90A04DB7A5 (DESKTOP-E41IK6R-21456).
[2025.06.02-01.36.13:144][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.02-01.36.13:144][  0]LogNNERuntimeORT: 0: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.02-01.36.13:144][  0]LogNNERuntimeORT: 1: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.02-01.36.13:144][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.02-01.36.13:144][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.02-01.36.13:578][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.02-01.36.13:579][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.02-01.36.13:592][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.02-01.36.14:938][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.06.02-01.36.14:938][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.06.02-01.36.15:236][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.02-01.36.17:799][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.02-01.36.17:817][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.02-01.36.17:818][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group *********:6666.
[2025.06.02-01.36.17:818][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:63005'.
[2025.06.02-01.36.17:820][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '*********:6666'
[2025.06.02-01.36.17:820][  0]LogUdpMessaging: Display: Added local interface '************' to multicast group '*********:6666'
[2025.06.02-01.36.17:820][  0]LogUdpMessaging: Display: Added local interface '***********' to multicast group '*********:6666'
[2025.06.02-01.36.17:820][  0]LogUdpMessaging: Display: Added local interface '************' to multicast group '*********:6666'
[2025.06.02-01.36.18:509][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.06.02-01.36.18:930][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.02-01.36.19:016][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.02-01.36.19:069][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.02-01.36.19:069][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.02-01.36.19:904][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.02-01.36.19:914][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.02-01.36.19:914][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.02-01.36.19:920][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.02-01.36.19:931][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.02-01.36.19:946][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.02-01.36.19:947][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.02-01.36.19:947][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.02-01.36.19:960][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.02-01.36.19:970][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.02-01.36.19:974][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.02-01.36.19:975][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.02-01.36.19:975][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.02-01.36.19:986][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.02-01.36.19:995][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.02-01.36.20:001][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.02-01.36.20:012][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.02-01.36.20:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.02-01.36.20:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.02-01.36.20:034][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.02-01.36.20:069][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.02-01.36.20:070][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.02-01.36.20:070][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.02-01.36.20:082][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.02-01.36.20:093][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.02-01.36.20:105][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.02-01.36.20:115][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.02-01.36.20:116][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.02-01.36.20:117][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.02-01.36.20:125][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.02-01.36.20:126][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.02-01.36.20:133][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.02-01.36.20:133][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.02-01.36.20:134][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.02-01.36.20:134][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.02-01.36.21:291][  0]SourceControl: Revision control is disabled
[2025.06.02-01.36.21:436][  0]SourceControl: Revision control is disabled
[2025.06.02-01.36.21:628][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.02-01.36.21:683][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.43ms
[2025.06.02-01.36.23:925][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.02-01.36.25:557][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.06.02-01.36.25:618][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.06.02-01.36.32:526][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.36.32:540][  0]LogSkeletalMesh: Built Skeletal Mesh [6.99s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.06.02-01.36.32:697][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.02-01.36.32:697][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.02-01.36.32:698][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.02-01.36.32:698][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.02-01.36.32:698][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.02-01.36.32:698][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.02-01.36.32:945][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.02-01.36.32:945][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.02-01.36.33:543][  0]LogCollectionManager: Loaded 0 collections in 0.000748 seconds
[2025.06.02-01.36.33:545][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.06.02-01.36.33:549][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.02-01.36.33:549][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.06.02-01.36.33:679][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.06.02-01.36.33:679][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.02-01.36.33:679][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.06.02-01.36.33:679][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.06.02-01.36.33:679][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.06.02-01.36.33:679][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.06.02-01.36.33:679][  0]LogBlenderLink: Waiting for client connection...
[2025.06.02-01.36.33:785][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.02-01.36.33:785][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.02-01.36.33:786][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.02-01.36.33:786][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.02-01.36.33:786][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.02-01.36.33:786][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.02-01.36.33:827][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.02-01.36.33:827][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.02-01.36.33:863][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-02T01:36:33.863Z using C
[2025.06.02-01.36.33:864][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.02-01.36.33:864][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.02-01.36.33:864][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.02-01.36.33:898][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.02-01.36.33:898][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.02-01.36.33:898][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.02-01.36.33:898][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000076
[2025.06.02-01.36.33:898][  0]LogFab: Display: Logging in using persist
[2025.06.02-01.36.33:898][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.02-01.36.34:030][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.06.02-01.36.34:030][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.02-01.36.34:044][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.06.02-01.36.34:044][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.02-01.36.34:224][  0]LogEngine: Initializing Engine...
[2025.06.02-01.36.34:227][  0]LogAssetEditorSubsystem: Warning: Something went wrong while loading recent assets! Num Recent Assets  = 17, Num Recent Asset Editors = 18
[2025.06.02-01.36.34:239][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.02-01.36.34:239][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.02-01.36.34:388][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.02-01.36.34:509][  0]LogTexture: Display: Waiting for textures to be ready 34/44 (/Engine/EngineMaterials/Black_1x1_EXR_Texture_VT) ...
[2025.06.02-01.36.34:558][  0]LogTexture: Display: Waiting for textures to be ready 41/44 (/Engine/EngineMaterials/PreintegratedSkinBRDF) ...
[2025.06.02-01.36.34:591][  0]LogTexture: Display: Waiting for textures to be ready 43/44 (/Engine/EngineMaterials/EnergyConservation/SheenLegacy_Energy) ...
[2025.06.02-01.36.34:594][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.02-01.36.34:634][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.06.02-01.36.34:634][  0]LogInit: Texture streaming: Enabled
[2025.06.02-01.36.34:704][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.02-01.36.34:721][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.02-01.36.34:771][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.02-01.36.34:772][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.02-01.36.34:772][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.02-01.36.34:772][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.02-01.36.34:772][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.02-01.36.34:772][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.02-01.36.34:772][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.02-01.36.34:772][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.02-01.36.34:772][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.02-01.36.34:772][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.02-01.36.34:772][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.02-01.36.34:772][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.02-01.36.34:772][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.02-01.36.34:772][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.02-01.36.34:772][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.02-01.36.34:794][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.02-01.36.34:859][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.02-01.36.34:860][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.02-01.36.34:861][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.02-01.36.34:861][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.02-01.36.34:863][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.02-01.36.34:863][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.02-01.36.34:864][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.02-01.36.34:864][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.02-01.36.34:864][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.02-01.36.34:865][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.02-01.36.34:865][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.02-01.36.34:910][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.02-01.36.34:915][  0]LogInit: Undo buffer set to 256 MB
[2025.06.02-01.36.34:915][  0]LogInit: Transaction tracking system initialized
[2025.06.02-01.36.34:982][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.06.02-01.36.35:502][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 1.56ms
[2025.06.02-01.36.35:511][  0]LocalizationService: Localization service is disabled
[2025.06.02-01.36.35:587][  0]LogTimingProfiler: Initialize
[2025.06.02-01.36.35:587][  0]LogTimingProfiler: OnSessionChanged
[2025.06.02-01.36.35:587][  0]LoadingProfiler: Initialize
[2025.06.02-01.36.35:587][  0]LoadingProfiler: OnSessionChanged
[2025.06.02-01.36.35:588][  0]LogNetworkingProfiler: Initialize
[2025.06.02-01.36.35:589][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.02-01.36.35:589][  0]LogMemoryProfiler: Initialize
[2025.06.02-01.36.35:589][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.02-01.36.36:256][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.06.02-01.36.36:267][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.06.02-01.36.36:927][  0]LogPython: Using Python 3.11.8
[2025.06.02-01.36.38:786][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.02-01.36.38:965][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.06.02-01.36.39:120][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.02-01.36.40:244][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.02-01.36.40:244][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.02-01.36.40:563][  0]LogEditorDataStorage: Initializing
[2025.06.02-01.36.40:563][  0]LogEditorDataStorage: Initialized
[2025.06.02-01.36.40:565][  0]LogWindows: Attached monitors:
[2025.06.02-01.36.40:565][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.02-01.36.40:565][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.02-01.36.40:565][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.02-01.36.40:565][  0]LogWindows: Found 3 attached monitors.
[2025.06.02-01.36.40:565][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.02-01.36.40:603][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.02-01.36.40:604][  0]SourceControl: Revision control is disabled
[2025.06.02-01.36.40:604][  0]LogUnrealEdMisc: Loading editor; pre map load, took 63.041
[2025.06.02-01.36.40:605][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.02-01.36.40:607][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.36.40:607][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.36.40:622][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.02-01.36.40:665][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.02-01.36.40:667][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.04ms
[2025.06.02-01.36.40:674][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.06.02-01.36.40:674][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.06.02-01.36.40:675][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.06.02-01.36.40:675][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.06.02-01.36.40:675][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.06.02-01.36.41:420][  0]LogAssetRegistry: Display: Asset registry cache written as 44.1 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.06.02-01.36.43:738][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-01.36.43:752][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-01.36.43:754][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-01.36.43:755][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.06.02-01.36.43:755][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.06.02-01.36.43:755][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-01.36.43:757][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.06.02-01.36.47:683][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.02-01.36.47:800][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.02-01.36.48:221][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.36.48:227][  0]LogSkeletalMesh: Built Skeletal Mesh [0.43s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.02-01.36.48:242][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.02-01.36.48:243][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.02-01.36.48:637][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.36.48:639][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.02-01.36.49:751][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.36.49:755][  0]LogSkeletalMesh: Built Skeletal Mesh [1.51s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.02-01.36.49:896][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.02-01.36.50:120][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.36.50:124][  0]LogSkeletalMesh: Built Skeletal Mesh [0.23s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.02-01.36.50:131][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.02-01.36.50:484][  0]LogWorldPartition: Display: WorldPartition initialize took 9.80 sec
[2025.06.02-01.36.50:563][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.02-01.36.55:696][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.36.55:711][  0]LogSkeletalMesh: Built Skeletal Mesh [5.58s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.02-01.36.56:313][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.02-01.36.56:533][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.34ms
[2025.06.02-01.36.56:536][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.02-01.36.56:537][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.523ms to complete.
[2025.06.02-01.36.56:590][  0]LogUnrealEdMisc: Total Editor Startup Time, took 79.028
[2025.06.02-01.36.56:693][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.02-01.36.56:795][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-01.36.56:839][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-01.36.56:881][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-01.36.56:924][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-01.36.56:992][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:001][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.02-01.36.57:002][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:010][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.02-01.36.57:011][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:015][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.02-01.36.57:016][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:037][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.02-01.36.57:053][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:060][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.02-01.36.57:067][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:075][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.02-01.36.57:076][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:081][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.02-01.36.57:082][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:093][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.02-01.36.57:095][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:099][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.02-01.36.57:101][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-01.36.57:109][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.02-01.36.59:279][  0]LogSlate: Took 0.013793 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.02-01.36.59:489][  0]LogStall: Startup...
[2025.06.02-01.36.59:490][  0]LogStall: Startup complete.
[2025.06.02-01.36.59:508][  0]LogLoad: (Engine Initialization) Total time: 81.95 seconds
[2025.06.02-01.36.59:884][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/CX/RO749IAXISCV8LT9J5BYI5) ...
[2025.06.02-01.36.59:921][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/GK/X2E2F4C7MIW7ZEGZ962NPQ) ...
[2025.06.02-01.36.59:955][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/1/2F/MHEXK6BJ1KVAC7HBYSCFW0) ...
[2025.06.02-01.37.00:013][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/5/IZ/ZJQCJTFMLN2BE3G5KXGJAZ) ...
[2025.06.02-01.37.00:034][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/7/40/3X2YNQMQX451AT39W6TXU8) ...
[2025.06.02-01.37.00:060][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/1U/9QUTGR1WIWGBXM0N6TQ1D0) ...
[2025.06.02-01.37.00:121][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/9/96/PHR108TB1MF9H9926QNOWQ) ...
[2025.06.02-01.37.00:202][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/B/OG/O63E6S0UGSLAKAAT7UN1QX) ...
[2025.06.02-01.37.00:232][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.02-01.37.00:232][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.02-01.37.00:389][  0]LogSlate: Took 0.021545 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.02-01.37.00:399][  0]LogSlate: Took 0.007201 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.02-01.37.00:418][  0]LogSlate: Took 0.017174 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.02-01.37.00:480][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.02-01.37.00:480][  0]LogStreaming: Display: FlushAsyncLoading(501): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-01.37.00:510][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.02-01.37.00:510][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.02-01.37.00:510][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.02-01.37.00:589][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.02-01.37.00:589][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.02-01.37.00:590][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.02-01.37.00:591][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.02-01.37.00:591][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.02-01.37.00:649][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.02-01.37.00:649][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.02-01.37.00:746][  0]LogSlate: Took 0.013961 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.02-01.37.00:858][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 17.90 ms. Compile time 10.18 ms, link time 7.57 ms.
[2025.06.02-01.37.00:946][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.37.00:951][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.02-01.37.00:952][  0]LogFab: Display: Logging in using exchange code
[2025.06.02-01.37.00:952][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.02-01.37.00:952][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.02-01.37.00:965][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.02-01.37.01:530][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.02-01.37.01:536][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 572.028 ms
[2025.06.02-01.37.01:868][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.06.02-01.37.02:004][  1]LogAssetRegistry: AssetRegistryGather time 0.3002s: AssetDataDiscovery 0.2399s, AssetDataGather 0.0117s, StoreResults 0.0486s. Wall time 57.4350s.
	NumCachedDirectories 0. NumUncachedDirectories 1886. NumCachedFiles 8088. NumUncachedFiles 0.
	BackgroundTickInterruptions 2.
[2025.06.02-01.37.02:048][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.02-01.37.02:048][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.06.02-01.37.02:184][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.02-01.37.02:256][  2]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 28.286205
[2025.06.02-01.37.02:257][  2]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.02-01.37.02:259][  2]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 28.358437
[2025.06.02-01.37.02:723][  9]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.02-01.37.03:288][ 19]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 29.334127
[2025.06.02-01.37.03:290][ 19]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.02-01.37.03:290][ 19]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 29.334127, Update Interval: 359.637451
[2025.06.02-01.37.04:962][ 52]LogSourceControl: Uncontrolled asset enumeration finished in 2.914564 seconds (Found 8064 uncontrolled assets)
[2025.06.02-01.37.09:524][279]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.37.19:534][ 91]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.37.29:543][912]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.37.39:551][731]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.37.49:561][538]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.37.52:518][676]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.02-01.37.59:571][ 12]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.38.09:575][530]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.38.19:587][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.38.29:589][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.38.39:605][ 98]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.38.49:606][636]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.38.59:613][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.39.09:618][753]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.39.19:624][810]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.39.29:631][876]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.39.39:637][950]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.39.49:640][ 30]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.39.59:647][ 96]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.40.09:653][ 97]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.40.19:654][ 99]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.40.29:663][110]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.40.39:669][132]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.40.49:679][213]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.40.59:686][278]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.41.09:690][308]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.41.19:698][291]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.41.29:700][277]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.41.39:701][285]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.41.49:703][366]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.41.59:712][349]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.42.09:721][340]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.42.19:727][327]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.42.29:734][316]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.42.39:744][216]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.42.49:745][125]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.42.59:751][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.43.02:881][336]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 388.984344
[2025.06.02-01.43.03:159][364]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.02-01.43.03:160][364]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 389.253693, Update Interval: 302.195496
[2025.06.02-01.43.09:749][985]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.43.19:752][972]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.43.29:760][916]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.43.39:765][907]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.43.49:772][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.43.59:774][ 58]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.44.09:781][135]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.44.19:785][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.44.29:791][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.44.39:790][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.44.49:791][346]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.44.59:792][415]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.45.09:794][487]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.45.19:795][556]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.45.29:799][630]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.45.39:800][692]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.45.49:799][772]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.45.59:809][852]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.46.09:814][925]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.46.19:817][  2]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.46.29:817][ 79]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.46.39:820][157]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.46.49:822][235]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.46.59:828][321]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.47.09:836][395]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.47.19:841][301]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.47.24:238][747]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-01.47.24:298][747]LogInterchangeEngine: Display: Interchange start importing source [C:/Users/<USER>/Desktop/Export/MH_Friend.fbx]
[2025.06.02-01.47.46:971][747]LogSlate: Took 0.000092 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.02-01.47.47:021][747]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.02-01.47.47:036][747]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.02-01.47.47:060][747]LogSkeletalMesh: Building Skeletal Mesh SKM_Face_Preview...
[2025.06.02-01.47.47:136][747]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.02-01.47.47:295][747]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.47.47:298][747]LogSkeletalMesh: Built Skeletal Mesh [0.24s] /Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview
[2025.06.02-01.47.52:509][747]LogSlate: Window 'Import Content' being destroyed
[2025.06.02-01.47.52:544][747]LogInterchangeEngine: [Pending] Importing
[2025.06.02-01.47.52:643][748]LogStreaming: Display: FlushAsyncLoading(517): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-01.47.52:643][748]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.47.55:511][809]LogStreaming: Display: FlushAsyncLoading(518): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-01.48.02:437][842]LogSlate: Window 'Message' being destroyed
[2025.06.02-01.48.02:481][842]Message dialog closed, result: Yes, title: Message, text: Failed to merge bones.

 This can happen if significant hierarchical changes have been made,
such as inserting a bone between nodes.

Would you like to regenerate the Skeleton from this mesh? This may invalidate or require recompression of animation data.

[2025.06.02-01.48.02:808][842]LogAnimationCompression: Display: Building compressed animation data for AnimSequence /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim (Required Memory Estimate: 5.61 MB)
[2025.06.02-01.48.02:934][842]LogAnimationCompression: Display: Building compressed animation data for AnimSequence /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim (Required Memory Estimate: 100.51 MB)
[2025.06.02-01.48.02:941][842]LogAnimationCompression: Display: Storing compressed animation data for /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim, at AnimationSequence/8cbf1b30fa21da3536bd953c9dd01015cdadf843
[2025.06.02-01.48.03:023][842]LogStreaming: Display: FlushAsyncLoading(519): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-01.48.03:205][842]LogSkeletalMesh: Building Skeletal Mesh MHI_Baoli_FaceMesh...
[2025.06.02-01.48.03:360][842]LogAnimationCompression: Display: Storing compressed animation data for /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim, at AnimationSequence/42049157abfac3f5284594d698880e4843f27176
[2025.06.02-01.48.05:214][842]LogSlate: Window 'Message' being destroyed
[2025.06.02-01.48.05:283][842]Message dialog closed, result: Yes, title: Message, text: Would you like to merge all SkeletalMeshes using this skeleton to ensure all bones are merged? This will require to load those SkeletalMeshes.
/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
/Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview
/Game/MetaHumans/MHI_Baoli/Face/MHI_Baoli_FaceMesh.MHI_Baoli_FaceMesh
[2025.06.02-01.48.06:207][842]LogSlate: Window 'Message' being destroyed
[2025.06.02-01.48.06:273][842]Message dialog closed, result: Ok, title: Message, text: Failed to merge SkeletalMesh 'MH_Friend_FaceMesh'.
[2025.06.02-01.48.06:864][842]LogSlate: Window 'Message' being destroyed
[2025.06.02-01.48.06:931][842]Message dialog closed, result: Ok, title: Message, text: Failed to merge SkeletalMesh 'SKM_Face_Preview'.
[2025.06.02-01.48.06:948][842]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MHI_Baoli_FaceMesh) ...
[2025.06.02-01.48.08:439][842]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MHI_Baoli/Face/MHI_Baoli_FaceMesh.MHI_Baoli_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-01.48.08:452][842]LogSkeletalMesh: Built Skeletal Mesh [5.25s] /Game/MetaHumans/MHI_Baoli/Face/MHI_Baoli_FaceMesh.MHI_Baoli_FaceMesh
[2025.06.02-01.48.09:544][842]LogSlate: Window 'Message' being destroyed
[2025.06.02-01.48.09:591][842]Message dialog closed, result: Ok, title: Message, text: Failed to merge SkeletalMesh 'MHI_Baoli_FaceMesh'.
[2025.06.02-01.48.09:608][843]LogSkeletalMesh: Building Skeletal Mesh MH_Friend...
[2025.06.02-01.48.09:608][843]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.48.13:587][905]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 699.689636
[2025.06.02-01.48.14:120][911]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.02-01.48.14:120][911]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 700.140442, Update Interval: 324.615631
[2025.06.02-01.48.14:650][917]LogSkeletalMesh: Built Skeletal Mesh [5.04s] /Game/MetaHumans/Test/MH_Friend.MH_Friend
[2025.06.02-01.48.14:952][918]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend) ...
[2025.06.02-01.48.14:991][918]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.02-01.48.14:998][918]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.14:998][918]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.15:099][919]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.06.02-01.48.15:122][919]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.06.02-01.48.15:311][919]LogInterchangeEngine: Display: Interchange import completed [C:/Users/<USER>/Desktop/Export/MH_Friend.fbx]
[2025.06.02-01.48.15:312][919]Interchange: Warning: [C:/Users/<USER>/Desktop/Export/MH_Friend.fbx : 'MH_Friend', SkeletalMesh] Imported skeletal mesh has some invalid bind poses. Skeletal mesh skinning has been rebind using the time zero pose.
[2025.06.02-01.48.15:351][919]LogInterchangeEngine: [Pending] Importing - Operation completed.
[2025.06.02-01.48.15:351][919]LogInterchangeEngine: [Success] Import Done
[2025.06.02-01.48.16:354][931]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.02-01.48.16:476][932]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.02-01.48.18:699][953]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.06.02-01.48.18:701][953]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/MetaHumans/Test/TestLevel' took 0.020
[2025.06.02-01.48.18:702][953]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton] ([1] browsable assets)...
[2025.06.02-01.48.18:703][953]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.02-01.48.18:805][953]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton]
[2025.06.02-01.48.18:805][953]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton]
[2025.06.02-01.48.18:845][953]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton_Auto1
[2025.06.02-01.48.18:845][953]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/Face_Archetype_Skeleton_Auto1913B7A5D473C3A0CDE507E9B11AF81FE.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton_Auto1.uasset'
[2025.06.02-01.48.18:849][953]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim] ([1] browsable assets)...
[2025.06.02-01.48.18:850][953]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.02-01.48.18:905][953]OBJ SavePackage:     Rendered thumbnail for [AnimSequence /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim]
[2025.06.02-01.48.18:905][953]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim]
[2025.06.02-01.48.19:112][953]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim_Auto1
[2025.06.02-01.48.19:112][953]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/mh_arkit_mapping_anim_Auto1F3E3A5DA46ECB8A56DC2A0946EB49974.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim_Auto1.uasset'
[2025.06.02-01.48.19:113][953]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim] ([1] browsable assets)...
[2025.06.02-01.48.19:182][953]OBJ SavePackage:     Rendered thumbnail for [AnimSequence /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim]
[2025.06.02-01.48.19:182][953]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim]
[2025.06.02-01.48.19:247][953]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim_Auto1
[2025.06.02-01.48.19:247][953]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/neckCorr_m_med_nrw_RBFSolver_ani0007B07D4993DA88CB5800A6E971FBE5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim_Auto1.uasset'
[2025.06.02-01.48.19:248][953]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Test/MH_Friend_PhysicsAsset] ([1] browsable assets)...
[2025.06.02-01.48.19:254][953]OBJ SavePackage:     Rendered thumbnail for [PhysicsAsset /Game/MetaHumans/Test/MH_Friend_PhysicsAsset.MH_Friend_PhysicsAsset]
[2025.06.02-01.48.19:254][953]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test/MH_Friend_PhysicsAsset]
[2025.06.02-01.48.19:256][953]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Test/MH_Friend_PhysicsAsset_Auto1
[2025.06.02-01.48.19:256][953]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_PhysicsAsset_Auto1C95D43C94F62A535D8F32D9BE22FC916.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test/MH_Friend_PhysicsAsset_Auto1.uasset'
[2025.06.02-01.48.19:257][953]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Test/MH_Friend] ([1] browsable assets)...
[2025.06.02-01.48.19:263][953]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/MetaHumans/Test/MH_Friend.MH_Friend]
[2025.06.02-01.48.19:263][953]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test/MH_Friend]
[2025.06.02-01.48.19:263][953]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/MetaHumans/Test/MH_Friend" FILE="H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test/MH_Friend_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.06.02-01.48.19:568][953]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Test/MH_Friend_Auto1
[2025.06.02-01.48.19:568][953]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_Auto1E6E8262445D7DDDA4E2DBA91102AFBDF.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test/MH_Friend_Auto1.uasset'
[2025.06.02-01.48.19:571][953]LogFileHelpers: Auto-saving content packages took 0.869
[2025.06.02-01.48.19:822][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.48.20:409][967]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Test/MH_Friend.MH_Friend
[2025.06.02-01.48.20:458][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.02-01.48.20:495][967]LogStreaming: Display: FlushAsyncLoading(520): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-01.48.20:560][967]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Engine/EditorMeshes/AssetViewer/Sphere_inversenormals) ...
[2025.06.02-01.48.20:719][967]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_6:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.02-01.48.21:648][970]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.02-01.48.23:327][  9]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton
[2025.06.02-01.48.23:347][  9]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.02-01.48.23:400][  9]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.02-01.48.23:693][  9]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.02-01.48.23:897][  9]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.02-01.48.23:934][  9]LogSlate: Took 0.011235 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.02-01.48.29:804][140]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-01.48.35:169][349]LogUObjectHash: Compacting FUObjectHashTables data took   0.91ms
[2025.06.02-01.48.36:245][349]LogSlate: Window 'Save Content' being destroyed
[2025.06.02-01.48.36:333][349]LogStall: Shutdown...
[2025.06.02-01.48.36:333][349]LogStall: Shutdown complete.
[2025.06.02-01.48.36:415][349]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:415][349]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:494][349]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:495][349]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:504][349]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:504][349]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:551][349]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.06.02-01.48.36:642][349]LogUObjectHash: Compacting FUObjectHashTables data took   0.93ms
[2025.06.02-01.48.36:655][349]Cmd: QUIT_EDITOR
[2025.06.02-01.48.36:655][350]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.02-01.48.36:662][350]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.02-01.48.36:663][350]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.02-01.48.36:663][350]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.02-01.48.36:671][350]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:671][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:673][350]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.06.02-01.48.36:685][350]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.02-01.48.36:685][350]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.02-01.48.36:687][350]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:687][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:688][350]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:688][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:688][350]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:688][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:688][350]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:688][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:689][350]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:689][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:689][350]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:689][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:689][350]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.02-01.48.36:689][350]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-01.48.36:689][350]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.02-01.48.36:690][350]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.06.02-01.48.36:690][350]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.06.02-01.48.36:690][350]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.06.02-01.48.36:692][350]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.02-01.48.36:692][350]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.02-01.48.36:692][350]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.06.02-01.48.36:692][350]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.02-01.48.36:692][350]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.02-01.48.36:695][350]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.02-01.48.36:699][350]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.02-01.48.36:699][350]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.02-01.48.36:700][350]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.02-01.48.36:702][350]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.02-01.48.36:703][350]LogExit: Preparing to exit.
[2025.06.02-01.48.36:779][350]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.06.02-01.48.37:225][350]LogEditorDataStorage: Deinitializing
[2025.06.02-01.48.37:833][350]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.06.02-01.48.37:843][350]LogExit: Editor shut down
[2025.06.02-01.48.37:845][350]LogExit: Transaction tracking system shut down
[2025.06.02-01.48.37:962][350]LogExit: Object subsystem successfully closed.
[2025.06.02-01.48.37:979][350]LogShaderCompilers: Display: Shaders left to compile 0
[2025.06.02-01.48.38:227][350]LogMemoryProfiler: Shutdown
[2025.06.02-01.48.38:227][350]LogNetworkingProfiler: Shutdown
[2025.06.02-01.48.38:227][350]LoadingProfiler: Shutdown
[2025.06.02-01.48.38:227][350]LogTimingProfiler: Shutdown
[2025.06.02-01.48.38:233][350]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.02-01.48.38:233][350]LogBlenderLink: Closing listener socket
[2025.06.02-01.48.38:233][350]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.02-01.48.38:575][350]LogChaosDD: Chaos Debug Draw Shutdown
[2025.06.02-01.48.38:585][350]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.06.02-01.48.38:585][350]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7BBDAAF189-437C-3D8E-9EBB-98B1D994800E%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.06.02-01.48.39:658][350]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.06.02-01.48.39:663][350]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.06.02-01.48.39:663][350]LogNFORDenoise: NFORDenoise function shutting down
[2025.06.02-01.48.39:663][350]RenderDocPlugin: plugin has been unloaded.
[2025.06.02-01.48.39:665][350]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.06.02-01.48.39:665][350]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.06.02-01.48.39:665][350]LogPakFile: Destroying PakPlatformFile
[2025.06.02-01.48.39:914][350]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.06.02-01.48.39:971][350]LogExit: Exiting.
[2025.06.02-01.48.39:987][350]Log file closed, 06/02/25 07:18:39
