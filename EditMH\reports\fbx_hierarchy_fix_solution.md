# FBX Hierarchy Issue - Extra Bone Problem

## PROBLEM IDENTIFIED

When importing the exported FBX into Unreal Engine, an extra bone called `MH_Friend_FaceMesh` appears at the root level, causing the "failed to merge bones" error.

### Current Export Result (WRONG):
```
MH_Friend_FaceMesh (extra bone from EMPTY)
  └─ root (armature)
    └─ pelvis
      └─ spine_01
        └─ spine_02...
```

### Original MetaHuman FBX Structure (CORRECT):
```
root (armature)
  └─ pelvis
    └─ spine_01
      └─ spine_02...
```

## ROOT CAUSE

The issue is in the FBX export settings in `export_fbx.py` line 275:

```python
object_types={'EMPTY', 'ARMATURE', 'MESH'}  # Include EMPTY for parent hierarchy
```

**What happens:**
1. We create a parent EMPTY object `MH_Friend_FaceMesh` with 0.01 scale
2. We parent the armature to this EMPTY
3. We include `'EMPTY'` in the FBX export object types
4. **FBX format converts parent EMPTY objects to bones in the skeleton hierarchy**
5. This creates an extra bone that doesn't exist in the original MetaHuman

## SOLUTION

### Option 1: Remove EMPTY from Export (RECOMMENDED)

**Change the export settings:**
```python
# BEFORE (creates extra bone):
object_types={'EMPTY', 'ARMATURE', 'MESH'}

# AFTER (correct hierarchy):
object_types={'ARMATURE', 'MESH'}
```

**But this requires handling the coordinate system differently:**

1. **Before Export**: Apply the 0.01 scale directly to the armature
2. **Export**: Without parent EMPTY
3. **After Export**: Restore original armature scale

### Option 2: Temporary Unparent During Export

1. **Before Export**: Temporarily unparent armature from EMPTY
2. **Export**: Only armature and meshes (no EMPTY)
3. **After Export**: Re-parent armature to EMPTY

### Option 3: Apply Scale to Armature Matrix

Instead of using parent EMPTY, apply the 0.01 scale directly to the armature's matrix_world:

```python
# Before export
armature.matrix_world = Matrix.Scale(0.01, 4) @ armature.matrix_world

# Export without EMPTY
object_types={'ARMATURE', 'MESH'}

# After export - restore
armature.matrix_world = original_matrix
```

## IMPLEMENTATION PLAN

### Step 1: Modify export_fbx.py

**In `export_fbx_file()` method around line 275:**

```python
# REMOVE EMPTY from object_types
bpy.ops.export_scene.fbx(
    filepath=export_path,
    use_selection=True,
    object_types={'ARMATURE', 'MESH'},  # REMOVED 'EMPTY'
    # ... rest of settings
)
```

### Step 2: Handle Coordinate System in Armature

**In `apply_metahuman_hierarchy()` method:**

```python
def apply_metahuman_hierarchy(self, context, export_armature, export_collection):
    # Store original armature matrix
    original_matrix = export_armature.matrix_world.copy()
    
    # Apply 0.01 scale directly to armature matrix (no parent EMPTY needed)
    scale_matrix = Matrix.Scale(0.01, 4)
    export_armature.matrix_world = scale_matrix @ export_armature.matrix_world
    
    # Store for restoration
    return original_matrix

def restore_metahuman_hierarchy(self, export_armature, original_matrix):
    # Restore original armature matrix
    export_armature.matrix_world = original_matrix
```

### Step 3: Update Cleanup Process

Remove all EMPTY-related cleanup code since we won't be creating parent EMPTYs.

## EXPECTED RESULT

After this fix:
- ✅ Exported FBX will have correct hierarchy: `root → pelvis → spine_01...`
- ✅ No extra `MH_Friend_FaceMesh` bone
- ✅ Unreal Engine bone merging will succeed
- ✅ Coordinate system will still be correct (0.01 scale applied to armature)

## VERIFICATION

1. Export FBX with the fix
2. Import both original and exported FBX into Blender
3. Compare armature hierarchies - should be identical
4. Import exported FBX into Unreal Engine - should merge successfully

## FILES TO MODIFY

- `EditMH/plugin/blender_dna_plugin/operators/export_fbx.py`
  - Remove 'EMPTY' from object_types
  - Modify hierarchy handling to use armature matrix instead of parent EMPTY
  - Update cleanup process
