﻿Log file open, 06/02/25 07:56:33
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=46204)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.234119
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-17FF281B4829680E0A9343ACA6F3EC45
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading IOS ini files took 0.04 seconds
LogConfig: Display: Loading Mac ini files took 0.04 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogAssetRegistry: Display: Asset registry cache read as 44.1 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.55ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.02-02.26.34:190][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.02-02.26.34:190][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.02-02.26.34:190][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.02-02.26.34:190][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.02-02.26.34:190][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.02-02.26.34:190][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.02-02.26.34:190][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.02-02.26.34:191][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.02-02.26.34:191][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.02-02.26.34:191][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.02-02.26.34:194][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.02-02.26.34:194][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.02-02.26.34:197][  0]LogRHI: Using Default RHI: D3D12
[2025.06.02-02.26.34:197][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.02-02.26.34:197][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.02-02.26.34:201][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.02-02.26.34:201][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.02-02.26.34:300][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.02-02.26.34:300][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.02-02.26.34:300][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.02-02.26.34:300][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.02-02.26.34:300][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.02-02.26.34:455][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.02-02.26.34:455][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.02-02.26.34:455][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.02-02.26.34:456][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.02-02.26.34:456][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.02-02.26.34:460][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.02-02.26.34:460][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.02-02.26.34:460][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.02-02.26.34:460][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.02-02.26.34:460][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.02-02.26.34:462][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.02-02.26.34:462][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.02-02.26.34:462][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.02-02.26.34:462][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.02-02.26.34:462][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-02.26.34:462][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.02-02.26.34:462][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.02-02.26.34:463][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.02-02.26.34:463][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.02-02.26.34:463][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.02-02.26.34:463][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.02-02.26.34:463][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.02-02.26.34:463][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.02-02.26.34:463][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.02-02.26.34:463][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.02-02.26.34:463][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.02-02.26.34:463][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.02-02.26.34:463][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.02-02.26.34:463][  0]LogInit: User: Shashank
[2025.06.02-02.26.34:463][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.02-02.26.34:463][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.02-02.26.35:211][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=74.4GB
[2025.06.02-02.26.35:211][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.02-02.26.35:211][  0]LogMemory: Process Physical Memory: 630.06 MB used, 663.13 MB peak
[2025.06.02-02.26.35:211][  0]LogMemory: Process Virtual Memory: 757.98 MB used, 757.98 MB peak
[2025.06.02-02.26.35:211][  0]LogMemory: Physical Memory: 34745.67 MB used,  30705.13 MB free, 65450.80 MB total
[2025.06.02-02.26.35:211][  0]LogMemory: Virtual Memory: 53803.11 MB used,  15743.69 MB free, 69546.80 MB total
[2025.06.02-02.26.35:211][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.02-02.26.35:216][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.02-02.26.35:225][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.02-02.26.35:225][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.02-02.26.35:225][  0]LogInit: Using OS detected language (en-GB).
[2025.06.02-02.26.35:225][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.02-02.26.35:227][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.02-02.26.35:227][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.02-02.26.35:472][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.02-02.26.35:472][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.02-02.26.35:472][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.02-02.26.35:485][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.02-02.26.35:485][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.02-02.26.35:599][  0]LogRHI: Using Default RHI: D3D12
[2025.06.02-02.26.35:599][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.02-02.26.35:599][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.02-02.26.35:599][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.02-02.26.35:599][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.02-02.26.35:599][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.02-02.26.35:600][  0]LogWindows: Attached monitors:
[2025.06.02-02.26.35:600][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.02-02.26.35:600][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.02-02.26.35:600][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.02-02.26.35:600][  0]LogWindows: Found 3 attached monitors.
[2025.06.02-02.26.35:600][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.02-02.26.35:600][  0]LogRHI: RHI Adapter Info:
[2025.06.02-02.26.35:600][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.02-02.26.35:600][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.02-02.26.35:600][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.02-02.26.35:600][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.02-02.26.35:633][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.02-02.26.35:699][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.02-02.26.35:699][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.02-02.26.35:784][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: Raster order views are supported
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.02-02.26.35:784][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000087570D15300)
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000087570D15580)
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000087570D15800)
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.02-02.26.35:810][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.02-02.26.35:810][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.02-02.26.35:821][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.02-02.26.35:826][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.02-02.26.35:833][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.06.02-02.26.35:833][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.06.02-02.26.35:856][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.02-02.26.35:856][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.02-02.26.35:856][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.02-02.26.35:856][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.02-02.26.35:856][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.02-02.26.35:856][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.02-02.26.35:856][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.02-02.26.35:856][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.02-02.26.35:856][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.02-02.26.35:880][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.02-02.26.35:896][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.02-02.26.35:896][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.02-02.26.35:910][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.02-02.26.35:910][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.02-02.26.35:910][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.02-02.26.35:910][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.02-02.26.35:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.02-02.26.35:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.02-02.26.35:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.02-02.26.35:938][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.02-02.26.35:938][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.02-02.26.35:938][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.02-02.26.35:938][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.02-02.26.35:952][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.02-02.26.35:952][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.02-02.26.35:969][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.02-02.26.35:969][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.02-02.26.35:969][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.02-02.26.35:969][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.02-02.26.35:969][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.02-02.26.36:013][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.02-02.26.36:015][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.02-02.26.36:016][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.02-02.26.36:016][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.02-02.26.36:017][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.02-02.26.36:017][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.02-02.26.36:017][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.02-02.26.36:017][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.02-02.26.36:017][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.02-02.26.36:078][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.02-02.26.36:078][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.02-02.26.36:079][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.02-02.26.36:079][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.02-02.26.36:080][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.02-02.26.36:080][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.02-02.26.36:080][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.02-02.26.36:080][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 19312 --child-id Zen_19312_Startup'
[2025.06.02-02.26.36:151][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.02-02.26.36:151][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.072 seconds
[2025.06.02-02.26.36:153][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.02-02.26.36:157][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.06.02-02.26.36:157][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.06ms. RandomReadSpeed=1124.17MBs, RandomWriteSpeed=279.36MBs. Assigned SpeedClass 'Local'
[2025.06.02-02.26.36:158][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.02-02.26.36:158][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.02-02.26.36:158][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.02-02.26.36:158][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.02-02.26.36:158][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.02-02.26.36:158][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.02-02.26.36:158][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.02-02.26.36:159][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/19312/).
[2025.06.02-02.26.36:159][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/7291F73B4E98D80A0E0727A9C80938DB/'.
[2025.06.02-02.26.36:159][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.02-02.26.36:159][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.02-02.26.36:160][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.02-02.26.36:160][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.02-02.26.36:562][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.02-02.26.37:134][  0]LogSlate: Using FreeType 2.10.0
[2025.06.02-02.26.37:134][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.02-02.26.37:134][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.02-02.26.37:134][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.02-02.26.37:136][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.02-02.26.37:136][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.02-02.26.37:136][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.02-02.26.37:136][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.02-02.26.37:159][  0]LogAssetRegistry: FAssetRegistry took 0.0021 seconds to start up
[2025.06.02-02.26.37:160][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.02-02.26.37:165][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.02-02.26.37:165][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.06.02-02.26.37:329][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-02.26.37:330][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.02-02.26.37:330][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.02-02.26.37:330][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.02-02.26.37:340][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.02-02.26.37:340][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.02-02.26.37:363][  0]LogDeviceProfileManager: Active device profile: [000008758DE7CE00][000008758C030000 66] WindowsEditor
[2025.06.02-02.26.37:363][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.02-02.26.37:363][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.02-02.26.37:366][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.02-02.26.37:366][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.02-02.26.37:395][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:396][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.02-02.26.37:396][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-02.26.37:396][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:396][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.02-02.26.37:396][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-02.26.37:396][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:397][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.02-02.26.37:397][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-02.26.37:397][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:397][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.02-02.26.37:397][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-02.26.37:397][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-02.26.37:398][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:399][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-02.26.37:399][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:399][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.02-02.26.37:399][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.02-02.26.37:399][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:399][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.02-02.26.37:399][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.02-02.26.37:400][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-02.26.37:401][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.02-02.26.37:401][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.02-02.26.37:401][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.02-02.26.37:552][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.02-02.26.37:552][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.06.02-02.26.37:552][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.02-02.26.37:552][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.02-02.26.37:552][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.02-02.26.37:677][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.02-02.26.37:697][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.02-02.26.37:707][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.02-02.26.37:709][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.02-02.26.37:900][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.02-02.26.37:900][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.02-02.26.37:905][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.02-02.26.37:905][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.02-02.26.37:906][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.06.02-02.26.37:910][  0]LogLiveCoding: Display: Waiting for server
[2025.06.02-02.26.37:922][  0]LogSlate: Border
[2025.06.02-02.26.37:922][  0]LogSlate: BreadcrumbButton
[2025.06.02-02.26.37:922][  0]LogSlate: Brushes.Title
[2025.06.02-02.26.37:922][  0]LogSlate: Default
[2025.06.02-02.26.37:922][  0]LogSlate: Icons.Save
[2025.06.02-02.26.37:922][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.02-02.26.37:922][  0]LogSlate: ListView
[2025.06.02-02.26.37:923][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.02-02.26.37:923][  0]LogSlate: SoftwareCursor_Grab
[2025.06.02-02.26.37:923][  0]LogSlate: TableView.DarkRow
[2025.06.02-02.26.37:923][  0]LogSlate: TableView.Row
[2025.06.02-02.26.37:923][  0]LogSlate: TreeView
[2025.06.02-02.26.38:014][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.02-02.26.38:015][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.02-02.26.38:016][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.777 ms
[2025.06.02-02.26.38:025][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.02-02.26.38:043][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.02-02.26.38:043][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.02-02.26.38:043][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.02-02.26.38:043][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.02-02.26.38:189][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.02-02.26.38:193][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.02-02.26.38:193][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.02-02.26.38:193][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:53586'.
[2025.06.02-02.26.38:196][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '230.0.0.1:6666'
[2025.06.02-02.26.38:196][  0]LogUdpMessaging: Display: Added local interface '172.24.112.1' to multicast group '230.0.0.1:6666'
[2025.06.02-02.26.38:196][  0]LogUdpMessaging: Display: Added local interface '172.31.80.1' to multicast group '230.0.0.1:6666'
[2025.06.02-02.26.38:196][  0]LogUdpMessaging: Display: Added local interface '172.26.208.1' to multicast group '230.0.0.1:6666'
[2025.06.02-02.26.38:229][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.02-02.26.38:229][  0]LogNNERuntimeORT: 0: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.02-02.26.38:229][  0]LogNNERuntimeORT: 1: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.02-02.26.38:229][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.02-02.26.38:229][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.02-02.26.38:332][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.02-02.26.38:332][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.02-02.26.38:345][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.02-02.26.38:493][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.06.02-02.26.38:493][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.06.02-02.26.38:527][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.02-02.26.38:620][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 31F34E081FEA4D578000000000006D00 | Instance: CC84C1394AAB9912EF032E9F4D5B4993 (DESKTOP-E41IK6R-19312).
[2025.06.02-02.26.38:870][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.06.02-02.26.38:973][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.02-02.26.39:197][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.02-02.26.39:410][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.06.02-02.26.39:479][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.06.02-02.26.45:269][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-02.26.45:281][  0]LogSkeletalMesh: Built Skeletal Mesh [5.87s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.06.02-02.26.45:318][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.02-02.26.45:318][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.02-02.26.45:319][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.02-02.26.45:319][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.02-02.26.45:319][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.02-02.26.45:319][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.02-02.26.45:392][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.02-02.26.45:403][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.02-02.26.45:416][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.02-02.26.45:416][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.02-02.26.45:498][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.02-02.26.45:498][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.02-02.26.45:499][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.02-02.26.45:499][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.02-02.26.45:500][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.02-02.26.45:500][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.02-02.26.45:509][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.02-02.26.45:510][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.02-02.26.45:510][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.02-02.26.45:510][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.02-02.26.45:510][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.02-02.26.45:511][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.02-02.26.45:511][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.02-02.26.45:511][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.02-02.26.45:512][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.02-02.26.45:512][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.02-02.26.45:512][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.02-02.26.45:513][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.02-02.26.45:513][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.02-02.26.45:513][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.02-02.26.45:513][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.02-02.26.45:514][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.02-02.26.45:514][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.02-02.26.45:514][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.02-02.26.45:514][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.02-02.26.45:514][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.02-02.26.45:514][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.02-02.26.45:515][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.02-02.26.45:515][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.02-02.26.45:516][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.02-02.26.45:516][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.02-02.26.45:516][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.02-02.26.45:516][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.02-02.26.45:516][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.02-02.26.45:517][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.02-02.26.45:620][  0]SourceControl: Revision control is disabled
[2025.06.02-02.26.45:633][  0]SourceControl: Revision control is disabled
[2025.06.02-02.26.45:657][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.02-02.26.45:664][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.43ms
[2025.06.02-02.26.45:900][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.02-02.26.45:900][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.02-02.26.45:943][  0]LogCollectionManager: Loaded 0 collections in 0.000659 seconds
[2025.06.02-02.26.45:944][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.06.02-02.26.45:946][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.02-02.26.45:948][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.06.02-02.26.45:986][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.06.02-02.26.45:986][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.02-02.26.45:986][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.06.02-02.26.45:986][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.06.02-02.26.45:986][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.06.02-02.26.45:986][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.06.02-02.26.45:986][  0]LogBlenderLink: Waiting for client connection...
[2025.06.02-02.26.46:003][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.02-02.26.46:003][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.02-02.26.46:005][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.02-02.26.46:005][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.02-02.26.46:005][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.02-02.26.46:005][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.02-02.26.46:036][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.02-02.26.46:036][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.02-02.26.46:048][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-02T02:26:46.048Z using C
[2025.06.02-02.26.46:049][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.02-02.26.46:049][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.02-02.26.46:050][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.02-02.26.46:055][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.02-02.26.46:055][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.02-02.26.46:055][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.02-02.26.46:055][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000045
[2025.06.02-02.26.46:055][  0]LogFab: Display: Logging in using persist
[2025.06.02-02.26.46:055][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.02-02.26.46:085][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.06.02-02.26.46:085][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.02-02.26.46:096][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.06.02-02.26.46:096][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.02-02.26.46:199][  0]LogEngine: Initializing Engine...
[2025.06.02-02.26.46:201][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.02-02.26.46:202][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.02-02.26.46:269][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.02-02.26.46:282][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.02-02.26.46:292][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.06.02-02.26.46:292][  0]LogInit: Texture streaming: Enabled
[2025.06.02-02.26.46:300][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.02-02.26.46:305][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.02-02.26.46:310][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.02-02.26.46:311][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.02-02.26.46:311][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.02-02.26.46:311][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.02-02.26.46:311][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.02-02.26.46:311][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.02-02.26.46:311][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.02-02.26.46:311][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.02-02.26.46:311][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.02-02.26.46:311][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.02-02.26.46:311][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.02-02.26.46:311][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.02-02.26.46:311][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.02-02.26.46:311][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.02-02.26.46:311][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.02-02.26.46:314][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.02-02.26.46:370][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.02-02.26.46:371][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.02-02.26.46:372][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.02-02.26.46:372][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.02-02.26.46:372][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.02-02.26.46:372][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.02-02.26.46:374][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.02-02.26.46:374][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.02-02.26.46:374][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.02-02.26.46:375][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.02-02.26.46:375][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.02-02.26.46:380][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.02-02.26.46:382][  0]LogInit: Undo buffer set to 256 MB
[2025.06.02-02.26.46:382][  0]LogInit: Transaction tracking system initialized
[2025.06.02-02.26.46:393][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.06.02-02.26.46:440][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 1.16ms
[2025.06.02-02.26.46:443][  0]LocalizationService: Localization service is disabled
[2025.06.02-02.26.46:453][  0]LogTimingProfiler: Initialize
[2025.06.02-02.26.46:453][  0]LogTimingProfiler: OnSessionChanged
[2025.06.02-02.26.46:453][  0]LoadingProfiler: Initialize
[2025.06.02-02.26.46:453][  0]LoadingProfiler: OnSessionChanged
[2025.06.02-02.26.46:453][  0]LogNetworkingProfiler: Initialize
[2025.06.02-02.26.46:454][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.02-02.26.46:454][  0]LogMemoryProfiler: Initialize
[2025.06.02-02.26.46:454][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.02-02.26.46:578][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.06.02-02.26.46:587][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.06.02-02.26.46:620][  0]LogPython: Using Python 3.11.8
[2025.06.02-02.26.47:520][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.02-02.26.47:531][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.06.02-02.26.47:616][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.02-02.26.47:616][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.02-02.26.47:653][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.02-02.26.47:670][  0]LogEditorDataStorage: Initializing
[2025.06.02-02.26.47:671][  0]LogEditorDataStorage: Initialized
[2025.06.02-02.26.47:672][  0]LogWindows: Attached monitors:
[2025.06.02-02.26.47:672][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.02-02.26.47:672][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.02-02.26.47:672][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.02-02.26.47:672][  0]LogWindows: Found 3 attached monitors.
[2025.06.02-02.26.47:672][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.02-02.26.47:675][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.02-02.26.47:688][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.02-02.26.47:689][  0]SourceControl: Revision control is disabled
[2025.06.02-02.26.47:689][  0]LogUnrealEdMisc: Loading editor; pre map load, took 14.275
[2025.06.02-02.26.47:690][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.02-02.26.47:691][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.26.47:692][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.26.47:732][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.02-02.26.47:733][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.50ms
[2025.06.02-02.26.47:739][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.06.02-02.26.47:739][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.06.02-02.26.47:741][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.06.02-02.26.47:741][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.06.02-02.26.47:741][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.06.02-02.26.48:446][  0]LogAssetRegistry: Display: Asset registry cache written as 44.1 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.06.02-02.26.50:579][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-02.26.50:584][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-02.26.50:584][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-02.26.50:586][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.06.02-02.26.50:586][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.06.02-02.26.50:586][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.02-02.26.50:587][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.06.02-02.26.52:528][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.02-02.26.52:572][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.02-02.26.52:951][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-02.26.52:955][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.02-02.26.52:967][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.02-02.26.52:970][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.02-02.26.53:336][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-02.26.53:338][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.02-02.26.54:392][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-02.26.54:394][  0]LogSkeletalMesh: Built Skeletal Mesh [1.43s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.02-02.26.54:497][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.02-02.26.54:701][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-02.26.54:704][  0]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.02-02.26.54:706][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.02-02.26.55:013][  0]LogWorldPartition: Display: WorldPartition initialize took 7.27 sec
[2025.06.02-02.26.55:088][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.02-02.26.59:605][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.02-02.26.59:618][  0]LogSkeletalMesh: Built Skeletal Mesh [4.91s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.02-02.27.00:180][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.02-02.27.00:393][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.11ms
[2025.06.02-02.27.00:394][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.02-02.27.00:395][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.979ms to complete.
[2025.06.02-02.27.00:400][  0]LogUnrealEdMisc: Total Editor Startup Time, took 26.986
[2025.06.02-02.27.00:553][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.02-02.27.00:624][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-02.27.00:669][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-02.27.00:713][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-02.27.00:760][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.02-02.27.00:796][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:796][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.02-02.27.00:796][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:797][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.02-02.27.00:797][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:797][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.02-02.27.00:798][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:798][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.02-02.27.00:798][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:798][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.02-02.27.00:798][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:799][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.02-02.27.00:799][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:799][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.02-02.27.00:799][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:799][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.02-02.27.00:799][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:799][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.02-02.27.00:799][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.02-02.27.00:799][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.02-02.27.00:853][  0]LogSlate: Took 0.000120 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.02-02.27.01:026][  0]LogStall: Startup...
[2025.06.02-02.27.01:028][  0]LogStall: Startup complete.
[2025.06.02-02.27.01:035][  0]LogLoad: (Engine Initialization) Total time: 27.62 seconds
[2025.06.02-02.27.01:226][  0]LogAssetRegistry: AssetRegistryGather time 0.0748s: AssetDataDiscovery 0.0130s, AssetDataGather 0.0105s, StoreResults 0.0513s. Wall time 24.0690s.
	NumCachedDirectories 0. NumUncachedDirectories 1886. NumCachedFiles 8088. NumUncachedFiles 0.
	BackgroundTickInterruptions 2.
[2025.06.02-02.27.01:251][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.02-02.27.01:251][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.06.02-02.27.01:362][  0]LogSourceControl: Uncontrolled asset enumeration finished in 0.111388 seconds (Found 8064 uncontrolled assets)
[2025.06.02-02.27.01:540][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.02-02.27.01:540][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.02-02.27.01:693][  0]LogSlate: Took 0.000145 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.02-02.27.01:696][  0]LogSlate: Took 0.000120 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.02-02.27.01:697][  0]LogSlate: Took 0.000103 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.02-02.27.01:741][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.02-02.27.01:741][  0]LogStreaming: Display: FlushAsyncLoading(501): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-02.27.01:742][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.02-02.27.01:743][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.02-02.27.01:743][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.02-02.27.01:789][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.02-02.27.01:789][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.02-02.27.01:791][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.02-02.27.01:791][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.02-02.27.01:791][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.02-02.27.01:834][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.02-02.27.01:834][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.02-02.27.01:869][  0]LogSlate: Took 0.000341 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.02-02.27.01:925][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.01:928][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.02-02.27.01:928][  0]LogFab: Display: Logging in using exchange code
[2025.06.02-02.27.01:928][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.02-02.27.01:928][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.02-02.27.01:928][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.02-02.27.01:958][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.02-02.27.01:969][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 39.824 ms
[2025.06.02-02.27.01:977][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.06.02-02.27.02:077][  1]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 31.23 ms. Compile time 24.37 ms, link time 6.61 ms.
[2025.06.02-02.27.02:292][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.02-02.27.03:061][ 15]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 16.949749
[2025.06.02-02.27.03:062][ 15]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.02-02.27.03:062][ 15]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17.004881
[2025.06.02-02.27.03:498][ 23]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.02-02.27.04:054][ 34]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 17.946514
[2025.06.02-02.27.04:056][ 34]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.02-02.27.04:056][ 34]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17.946514, Update Interval: 321.262848
[2025.06.02-02.27.06:217][ 83]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-02.27.06:410][ 83]LogInterchangeEngine: Display: Interchange start importing source [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX]
[2025.06.02-02.27.25:686][ 83]LogSlate: Window 'Import Content' being destroyed
[2025.06.02-02.27.25:747][ 83]LogInterchangeEngine: [Pending] Importing
[2025.06.02-02.27.25:888][ 84]LogStreaming: Display: FlushAsyncLoading(518): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-02.27.25:889][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-02.27.25:935][ 85]LogStreaming: Display: FlushAsyncLoading(519): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-02.27.32:167][213]LogStreaming: Display: FlushAsyncLoading(520): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-02.27.35:268][278]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.02-02.27.35:936][290]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-02.27.38:637][328]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Test/MH_Friend_FaceMesh_Skeleton.MH_Friend_FaceMesh_Skeleton
[2025.06.02-02.27.38:642][328]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.02-02.27.38:651][328]LogStreaming: Display: FlushAsyncLoading(521): 1 QueuedPackages, 0 AsyncPackages
[2025.06.02-02.27.38:734][328]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.02-02.27.41:305][328]LogSkeletalMesh: Built Skeletal Mesh [6.04s] /Game/MetaHumans/Test/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.02-02.27.41:345][328]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_0:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.02-02.27.41:627][328]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.02-02.27.41:975][328]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.02-02.27.42:006][328]LogSlate: Took 0.000111 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.02-02.27.42:251][328]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.02-02.27.42:320][328]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.02-02.27.42:327][328]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.27.42:327][328]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.42:468][329]LogUObjectHash: Compacting FUObjectHashTables data took   0.93ms
[2025.06.02-02.27.42:599][329]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.06.02-02.27.43:113][329]LogInterchangeEngine: Display: Interchange import completed [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX]
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD0' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD1' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD2' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD3' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD4' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD5' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD6' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:113][329]Interchange: Warning: [C:/Users/<USER>/Downloads/MH_Friend_FaceMesh.FBX : 'MH_Friend_FaceMesh', SkeletalMesh] No smoothing group information was found for this mesh 'MH_Friend_FaceMesh_LOD7' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.06.02-02.27.43:235][329]LogInterchangeEngine: [Pending] Importing - Operation completed.
[2025.06.02-02.27.43:235][329]LogInterchangeEngine: [Success] Import Done
[2025.06.02-02.27.43:661][334]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.02-02.27.43:787][335]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.02-02.27.45:527][351]LogSlate: Window 'MH_Friend_FaceMesh_Skeleton' being destroyed
[2025.06.02-02.27.45:564][351]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.27.45:564][351]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.45:591][351]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.27.45:591][351]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.45:624][351]LogUObjectHash: Compacting FUObjectHashTables data took   1.05ms
[2025.06.02-02.27.45:942][357]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.02-02.27.46:956][378]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.06.02-02.27.48:089][378]LogSlate: Window 'Save Content' being destroyed
[2025.06.02-02.27.48:202][378]LogStall: Shutdown...
[2025.06.02-02.27.48:202][378]LogStall: Shutdown complete.
[2025.06.02-02.27.48:255][378]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.06.02-02.27.48:313][378]Cmd: QUIT_EDITOR
[2025.06.02-02.27.48:313][379]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.02-02.27.48:320][379]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.02-02.27.48:320][379]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.02-02.27.48:320][379]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.02-02.27.48:329][379]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.27.48:329][379]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.48:329][379]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.06.02-02.27.48:342][379]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.02-02.27.48:342][379]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.02-02.27.48:344][379]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.27.48:344][379]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.48:345][379]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.27.48:345][379]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.48:346][379]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.02-02.27.48:346][379]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.02-02.27.48:347][379]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.02-02.27.48:349][379]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.06.02-02.27.48:349][379]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.06.02-02.27.48:349][379]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.06.02-02.27.48:350][379]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.02-02.27.48:350][379]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.02-02.27.48:350][379]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.06.02-02.27.48:350][379]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.02-02.27.48:350][379]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.02-02.27.48:352][379]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.02-02.27.48:357][379]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.02-02.27.48:357][379]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.02-02.27.48:358][379]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.02-02.27.48:359][379]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.02-02.27.48:359][379]LogExit: Preparing to exit.
[2025.06.02-02.27.48:401][379]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.06.02-02.27.49:304][379]LogEditorDataStorage: Deinitializing
[2025.06.02-02.27.49:923][379]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.06.02-02.27.49:931][379]LogExit: Editor shut down
[2025.06.02-02.27.49:935][379]LogExit: Transaction tracking system shut down
[2025.06.02-02.27.50:060][379]LogExit: Object subsystem successfully closed.
[2025.06.02-02.27.50:108][379]LogShaderCompilers: Display: Shaders left to compile 0
[2025.06.02-02.27.50:536][379]LogMemoryProfiler: Shutdown
[2025.06.02-02.27.50:536][379]LogNetworkingProfiler: Shutdown
[2025.06.02-02.27.50:536][379]LoadingProfiler: Shutdown
[2025.06.02-02.27.50:536][379]LogTimingProfiler: Shutdown
[2025.06.02-02.27.50:541][379]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.02-02.27.50:541][379]LogBlenderLink: Closing listener socket
[2025.06.02-02.27.50:541][379]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.02-02.27.50:894][379]LogChaosDD: Chaos Debug Draw Shutdown
[2025.06.02-02.27.50:931][379]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.06.02-02.27.50:931][379]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7BB1C962B0-4076-91C7-AAE6-039C6429829C%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.06.02-02.27.51:991][379]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.06.02-02.27.51:996][379]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.06.02-02.27.51:996][379]LogNFORDenoise: NFORDenoise function shutting down
[2025.06.02-02.27.51:996][379]RenderDocPlugin: plugin has been unloaded.
[2025.06.02-02.27.51:996][379]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.06.02-02.27.51:997][379]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.06.02-02.27.51:997][379]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.06.02-02.27.51:997][379]LogPakFile: Destroying PakPlatformFile
[2025.06.02-02.27.52:159][379]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.06.02-02.27.52:216][379]LogExit: Exiting.
[2025.06.02-02.27.52:236][379]Log file closed, 06/02/25 07:57:52
