#!/usr/bin/env python3
"""
Build script for the Blender MetaHuman DNA Plugin.
This script creates a ZIP file that can be installed in Blender.

Usage:
    python build_plugin.py --version 1.0.0 --output-dir ../../builds --docs

This will:
1. Clean the build directory
2. Copy the plugin files to a temporary directory
3. Copy the necessary DNACalib files
4. Generate documentation if requested
5. Create a ZIP file with the correct structure for Blender plugin installation
6. Save the ZIP file to the builds directory with version information
"""

import os
import sys
import shutil
import zipfile
import argparse
import subprocess
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('build_plugin')

def parse_args():
    parser = argparse.ArgumentParser(description='Build the Blender MetaHuman DNA Plugin')
    parser.add_argument('--version', type=str, default='1.0.0', help='Version number for the plugin')
    parser.add_argument('--output-dir', type=str, default='builds', help='Output directory for the ZIP file')
    parser.add_argument('--docs', action='store_true', help='Generate documentation')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    parser.add_argument('--clean-only', action='store_true', help='Only clean the build directory')
    parser.add_argument('--include-tools', action='store_true', help='Include tools directory in the plugin')
    return parser.parse_args()

def get_project_root():
    """Get the project root directory"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.abspath(os.path.join(script_dir, '../..'))

def create_build_dir(project_root):
    """Create a temporary build directory"""
    build_dir = os.path.join(project_root, 'temp_build')
    if os.path.exists(build_dir):
        logger.info(f"Removing existing build directory: {build_dir}")
        shutil.rmtree(build_dir)
    logger.info(f"Creating build directory: {build_dir}")
    os.makedirs(build_dir)
    return build_dir

def copy_plugin_files(project_root, build_dir):
    """Copy plugin files to the build directory"""
    plugin_dir = os.path.join(project_root, 'plugin')
    target_dir = os.path.join(build_dir, 'blender_metahuman_dna')

    logger.info(f"Copying plugin files from {plugin_dir} to {target_dir}")

    # Copy the plugin files
    shutil.copytree(plugin_dir, target_dir)

    # Verify that the DNACalib files were copied
    dnacalib_dir = os.path.join(target_dir, 'dnacalib', 'py3.11')
    if not os.path.exists(dnacalib_dir):
        logger.error(f"DNACalib directory not found: {dnacalib_dir}")
        raise FileNotFoundError(f"DNACalib directory not found: {dnacalib_dir}")

    # Check for required files
    required_files = ['dna.py', '_py3dna.pyd', 'dnacalib.py', '_py3dnacalib.pyd']
    for file in required_files:
        file_path = os.path.join(dnacalib_dir, file)
        if not os.path.exists(file_path):
            logger.error(f"Required file not found: {file_path}")
            raise FileNotFoundError(f"Required file not found: {file_path}")

    logger.info("All required files copied successfully")
    return target_dir

def copy_tools_files(project_root, build_dir):
    """Copy tools files to the build directory"""
    tools_dir = os.path.join(project_root, 'tools')
    target_dir = os.path.join(build_dir, 'blender_metahuman_dna', 'tools')

    logger.info(f"Copying tools files from {tools_dir} to {target_dir}")

    # Create tools directory in the plugin
    os.makedirs(target_dir, exist_ok=True)

    # Copy specific tool files (exclude build directory and temp files)
    exclude_patterns = ['build', '__pycache__', '*.pyc', '*.pyo', 'temp_*', '*.log']

    for root, dirs, files in os.walk(tools_dir):
        # Skip build directory
        if 'build' in dirs:
            dirs.remove('build')

        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']

        for file in files:
            # Skip temporary and compiled files
            if any(file.endswith(ext) for ext in ['.pyc', '.pyo', '.log']) or file.startswith('temp_'):
                continue

            src_path = os.path.join(root, file)
            rel_path = os.path.relpath(src_path, tools_dir)
            dst_path = os.path.join(target_dir, rel_path)

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(dst_path), exist_ok=True)

            # Copy the file
            shutil.copy2(src_path, dst_path)
            logger.debug(f"Copied: {rel_path}")

    logger.info(f"Tools files copied to {target_dir}")
    return target_dir

def generate_documentation(project_root, build_dir, version):
    """Generate documentation for the plugin"""
    logger.info("Generating documentation...")

    # Create docs directory in the build
    docs_dir = os.path.join(build_dir, 'blender_metahuman_dna', 'docs')
    os.makedirs(docs_dir, exist_ok=True)

    # Copy the README.md to the docs directory
    readme_path = os.path.join(project_root, 'plugin', 'README.md')
    if os.path.exists(readme_path):
        shutil.copy(readme_path, os.path.join(docs_dir, 'README.md'))

    # Copy the plan document
    plan_path = os.path.join(project_root, 'tools', 'BlenderMetaHumanDNAPluginPlan.md')
    if os.path.exists(plan_path):
        shutil.copy(plan_path, os.path.join(docs_dir, 'DevelopmentPlan.md'))

    # Generate a simple HTML documentation from the README
    try:
        # Check if we have markdown installed
        subprocess.run([sys.executable, '-m', 'pip', 'show', 'markdown'],
                      check=False, capture_output=True, text=True)

        # Generate HTML from README.md
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>MetaHuman DNA Tools for Blender v{version}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
                h1 {{ color: #333; }}
                h2 {{ color: #444; margin-top: 30px; }}
                pre {{ background-color: #f4f4f4; padding: 10px; border-radius: 5px; }}
                code {{ font-family: Consolas, monospace; }}
            </style>
        </head>
        <body>
            <h1>MetaHuman DNA Tools for Blender v{version}</h1>
            <p>Documentation generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <hr>
            <div id="content">
            <!-- Content will be inserted here by the markdown converter -->
            </div>
        </body>
        </html>
        """

        with open(os.path.join(docs_dir, 'index.html'), 'w') as f:
            f.write(html_content)

        logger.info(f"Documentation generated in {docs_dir}")
        return docs_dir
    except Exception as e:
        logger.warning(f"Error generating HTML documentation: {e}")
        logger.info("Continuing with build process...")
        return docs_dir

def update_version_in_init(build_dir, version):
    """Update the version number in the __init__.py file"""
    init_path = os.path.join(build_dir, 'blender_metahuman_dna', '__init__.py')
    if os.path.exists(init_path):
        logger.info(f"Updating version in {init_path}")
        with open(init_path, 'r') as f:
            content = f.read()

        # Parse the version string into a tuple
        version_parts = version.split('.')
        if len(version_parts) < 3:
            version_parts.extend(['0'] * (3 - len(version_parts)))
        version_tuple = f"({version_parts[0]}, {version_parts[1]}, {version_parts[2]})"

        # Replace the version tuple in the file
        import re
        new_content = re.sub(r'"version":\s*\([^)]+\)', f'"version": {version_tuple}', content)

        with open(init_path, 'w') as f:
            f.write(new_content)

def create_zip_file(build_dir, output_dir, version):
    """Create a ZIP file from the build directory"""
    if not os.path.exists(output_dir):
        logger.info(f"Creating output directory: {output_dir}")
        os.makedirs(output_dir)

    timestamp = datetime.now().strftime('%Y%m%d')
    zip_filename = f'blender_metahuman_dna_v{version}_{timestamp}.zip'
    zip_path = os.path.join(output_dir, zip_filename)

    logger.info(f"Creating ZIP file: {zip_path}")

    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(build_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, build_dir)
                    zipf.write(file_path, arcname)

        logger.info(f"ZIP file created successfully: {zip_path}")
        return zip_path
    except Exception as e:
        logger.error(f"Error creating ZIP file: {e}")
        raise

def cleanup(build_dir):
    """Clean up the temporary build directory"""
    if os.path.exists(build_dir):
        logger.info(f"Cleaning up build directory: {build_dir}")
        shutil.rmtree(build_dir)

def main():
    args = parse_args()

    # Configure logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    project_root = get_project_root()
    output_dir = os.path.abspath(os.path.join(project_root, args.output_dir))

    logger.info(f"Building Blender MetaHuman DNA Plugin v{args.version}")
    logger.info(f"Project root: {project_root}")
    logger.info(f"Output directory: {output_dir}")

    try:
        # Create build directory
        build_dir = create_build_dir(project_root)

        if args.clean_only:
            logger.info("Clean-only mode specified. Exiting.")
            return

        # Copy plugin files
        target_dir = copy_plugin_files(project_root, build_dir)

        # Copy tools files if requested
        if args.include_tools:
            tools_target_dir = copy_tools_files(project_root, build_dir)
            logger.info(f"Tools included in plugin at: {tools_target_dir}")

        # Update version in __init__.py
        update_version_in_init(build_dir, args.version)

        # Generate documentation if requested
        if args.docs:
            docs_dir = generate_documentation(project_root, build_dir, args.version)

        # Create ZIP file
        zip_path = create_zip_file(build_dir, output_dir, args.version)

        # Clean up
        cleanup(build_dir)

        logger.info("Build completed successfully!")
        logger.info(f"Plugin ZIP file: {zip_path}")

        # Print relative path for easier reference
        rel_path = os.path.relpath(zip_path, project_root)
        logger.info(f"Relative path: {rel_path}")

    except Exception as e:
        logger.error(f"Build failed: {e}", exc_info=args.verbose)
        sys.exit(1)

if __name__ == '__main__':
    main()
